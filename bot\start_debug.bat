@echo off
chcp 65001 >nul
echo 微信Bot Webhook调试程序
echo =======================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3
    pause
    exit /b 1
)

REM 检查Flask是否安装
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 警告: 未安装Flask，正在安装...
    pip install flask
)

echo.
echo 使用方法:
echo 1. 监听所有wxid的消息:
echo    python debug_webhook.py
echo.
echo 2. 监听指定wxid的消息:
echo    python debug_webhook.py --wxid your_wxid
echo.
echo 3. 自定义端口:
echo    python debug_webhook.py --port 9000
echo.
echo 4. 启用调试模式:
echo    python debug_webhook.py --debug
echo.

REM 如果提供了参数，直接启动
if "%~1"=="" (
    echo 请选择启动方式，或直接运行: python debug_webhook.py [参数]
    pause
) else (
    echo 启动调试程序...
    python debug_webhook.py %*
)
