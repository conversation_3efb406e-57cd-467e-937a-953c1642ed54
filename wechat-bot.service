[Unit]
Description=YBA WeChat Bot Service
After=network.target mysql.service
Wants=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/wechat-bot
ExecStart=/usr/bin/python3 main.py
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/wechat-bot

# 环境变量文件
EnvironmentFile=-/opt/wechat-bot/.env

[Install]
WantedBy=multi-user.target
