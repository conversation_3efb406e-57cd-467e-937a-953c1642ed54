# Ubuntu服务部署指南

本指南将帮助您在Ubuntu系统上以systemd服务方式部署YBA微信机器人。

## 系统要求

- Ubuntu 18.04 或更高版本
- Python 3.6 或更高版本
- MySQL 5.7 或更高版本
- systemd (通常Ubuntu默认包含)
- root权限

## 快速部署

### 1. 准备部署文件

确保以下文件在您的项目目录中：
- `deploy_service.sh` - 自动部署脚本
- `wechat-bot.service` - systemd服务配置文件
- `manage_service.sh` - 服务管理脚本

### 2. 运行部署脚本

```bash
# 给脚本执行权限
chmod +x deploy_service.sh manage_service.sh

# 运行部署脚本
sudo ./deploy_service.sh
```

部署脚本将自动完成以下操作：
- 安装系统依赖
- 创建服务用户
- 复制项目文件到 `/opt/wechat-bot`
- 创建Python虚拟环境
- 安装Python依赖
- 配置systemd服务
- 启动服务

### 3. 配置环境变量

如果部署过程中提示缺少配置文件，请创建环境变量文件：

```bash
sudo nano /opt/wechat-bot/.env
```

参考以下配置模板：

```env
# Flask配置
SECRET_KEY=your-very-secret-key-here
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=False
FLASK_ENV=production

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your-mysql-user
MYSQL_PASSWORD=your-mysql-password
MYSQL_DB=your-database-name

# 管理员配置
ADMIN_TOKEN=your-admin-token

# 登录检查配置
ENABLE_PERIODIC_CHECK=true
LOGIN_CHECK_INTERVAL=300
```

### 4. 重启服务

配置完成后重启服务：

```bash
sudo systemctl restart wechat-bot
```

## 服务管理

使用提供的管理脚本进行日常管理：

```bash
# 查看服务状态
./manage_service.sh status

# 启动服务
sudo ./manage_service.sh start

# 停止服务
sudo ./manage_service.sh stop

# 重启服务
sudo ./manage_service.sh restart

# 查看日志
./manage_service.sh logs

# 实时查看日志
./manage_service.sh follow

# 检查配置
./manage_service.sh check
```

## 手动部署步骤

如果您希望手动部署，请按以下步骤操作：

### 1. 安装系统依赖

```bash
sudo apt update
sudo apt install -y python3-venv python3-pip mysql-client
```

### 2. 创建服务用户

```bash
sudo useradd --system --shell /bin/false --home-dir /nonexistent --no-create-home www-data
```

### 3. 创建安装目录

```bash
sudo mkdir -p /opt/wechat-bot
sudo chown www-data:www-data /opt/wechat-bot
```

### 4. 复制项目文件

```bash
sudo cp -r app/ V3/ main.py requirements.txt /opt/wechat-bot/
sudo chown -R www-data:www-data /opt/wechat-bot
```

### 5. 创建虚拟环境

```bash
cd /opt/wechat-bot
sudo -u www-data python3 -m venv venv
sudo -u www-data ./venv/bin/pip install -r requirements.txt
```

### 6. 配置systemd服务

```bash
sudo cp wechat-bot.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable wechat-bot
```

### 7. 创建配置文件

```bash
sudo nano /opt/wechat-bot/.env
# 添加必要的环境变量
sudo chown www-data:www-data /opt/wechat-bot/.env
sudo chmod 600 /opt/wechat-bot/.env
```

### 8. 启动服务

```bash
sudo systemctl start wechat-bot
```

## 故障排除

### 查看服务状态

```bash
sudo systemctl status wechat-bot
```

### 查看详细日志

```bash
sudo journalctl -u wechat-bot -f
```

### 常见问题

1. **服务启动失败**
   - 检查Python环境：`/opt/wechat-bot/venv/bin/python -c "import app"`
   - 检查配置文件：确保 `.env` 文件存在且配置正确
   - 检查权限：确保文件属于 `www-data` 用户

2. **数据库连接失败**
   - 检查MySQL服务是否运行：`sudo systemctl status mysql`
   - 验证数据库配置：用户名、密码、主机地址
   - 确保数据库用户有足够权限

3. **端口冲突**
   - 检查端口是否被占用：`sudo netstat -tlnp | grep :5000`
   - 修改 `.env` 文件中的 `FLASK_PORT` 配置

### 重新部署

如果需要重新部署：

```bash
# 停止服务
sudo systemctl stop wechat-bot

# 备份当前版本
sudo cp -r /opt/wechat-bot /opt/wechat-bot-backup-$(date +%Y%m%d)

# 重新运行部署脚本
sudo ./deploy_service.sh
```

## 安全建议

1. **文件权限**
   - 确保 `.env` 文件权限为 600
   - 项目目录属于 `www-data` 用户

2. **防火墙配置**
   ```bash
   sudo ufw allow 5000/tcp  # 如果使用默认端口
   ```

3. **定期备份**
   - 定期备份配置文件和数据库
   - 使用版本控制管理代码

4. **监控**
   - 设置日志轮转
   - 监控服务状态和资源使用

## 更新和维护

### 更新代码

```bash
# 停止服务
sudo systemctl stop wechat-bot

# 备份当前版本
sudo cp -r /opt/wechat-bot /opt/wechat-bot-backup-$(date +%Y%m%d)

# 更新代码文件
sudo cp -r app/ V3/ main.py /opt/wechat-bot/
sudo chown -R www-data:www-data /opt/wechat-bot

# 更新依赖（如果需要）
sudo -u www-data /opt/wechat-bot/venv/bin/pip install -r /opt/wechat-bot/requirements.txt

# 重启服务
sudo systemctl start wechat-bot
```

### 日志管理

配置日志轮转：

```bash
sudo nano /etc/logrotate.d/wechat-bot
```

添加以下内容：

```
/opt/wechat-bot/app.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
```

## 支持

如果遇到问题，请：

1. 查看服务日志：`sudo journalctl -u wechat-bot -f`
2. 检查配置：`./manage_service.sh check`
3. 查看系统资源：`htop` 或 `top`
4. 检查网络连接：`ping` 和 `telnet` 测试

---

**注意**: 请确保在生产环境中使用强密码和适当的安全配置。
