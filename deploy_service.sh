#!/bin/bash

# YBA微信机器人服务部署脚本
# 用于在Ubuntu上以systemd服务方式部署和启动微信机器人

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SERVICE_NAME="wechat-bot"
SERVICE_USER="www-data"
SERVICE_GROUP="www-data"
INSTALL_DIR="/opt/wechat-bot"
CURRENT_DIR=$(pwd)

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以root权限运行
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Ubuntu版本
    if ! grep -q "Ubuntu" /etc/os-release; then
        log_warning "此脚本专为Ubuntu设计，其他发行版可能需要调整"
    fi
    
    # 检查systemd
    if ! command -v systemctl &> /dev/null; then
        log_error "systemd未找到，无法创建服务"
        exit 1
    fi
    
    # 检查Python3
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        exit 1
    fi
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3未安装"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    apt update
    apt install -y python3-venv python3-pip mysql-client
    
    log_success "系统依赖安装完成"
}

# 创建服务用户
create_service_user() {
    log_info "检查服务用户..."
    
    if ! id "$SERVICE_USER" &>/dev/null; then
        log_info "创建服务用户: $SERVICE_USER"
        useradd --system --shell /bin/false --home-dir /nonexistent --no-create-home $SERVICE_USER
    else
        log_info "服务用户已存在: $SERVICE_USER"
    fi
}

# 创建安装目录
create_install_directory() {
    log_info "创建安装目录: $INSTALL_DIR"
    
    mkdir -p "$INSTALL_DIR"
    chown "$SERVICE_USER:$SERVICE_GROUP" "$INSTALL_DIR"
    chmod 755 "$INSTALL_DIR"
}

# 复制项目文件
copy_project_files() {
    log_info "复制项目文件到 $INSTALL_DIR"
    
    # 复制主要文件和目录
    cp -r app/ "$INSTALL_DIR/"
    cp main.py "$INSTALL_DIR/"
    cp requirements.txt "$INSTALL_DIR/"
    
    # 复制配置文件（如果存在）
    if [ -f ".env" ]; then
        cp .env "$INSTALL_DIR/"
        log_info "已复制.env配置文件"
    else
        log_warning ".env文件不存在，请稍后手动创建"
    fi
    
    # 设置权限
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "$INSTALL_DIR"
    chmod -R 755 "$INSTALL_DIR"
    
    # 保护敏感文件
    if [ -f "$INSTALL_DIR/.env" ]; then
        chmod 600 "$INSTALL_DIR/.env"
    fi
}

# 创建Python虚拟环境
create_virtual_environment() {
    log_info "创建Python虚拟环境..."
    
    cd "$INSTALL_DIR"
    sudo -u "$SERVICE_USER" python3 -m venv venv
    
    log_info "安装Python依赖..."
    sudo -u "$SERVICE_USER" "$INSTALL_DIR/venv/bin/pip" install --upgrade pip
    sudo -u "$SERVICE_USER" "$INSTALL_DIR/venv/bin/pip" install -r requirements.txt
    
    log_success "虚拟环境创建完成"
}

# 安装systemd服务
install_systemd_service() {
    log_info "安装systemd服务..."
    
    # 复制服务文件
    cp "$CURRENT_DIR/wechat-bot.service" /etc/systemd/system/
    
    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable "$SERVICE_NAME"
    
    log_success "systemd服务安装完成"
}

# 创建日志目录
create_log_directory() {
    log_info "创建日志目录..."
    
    mkdir -p /var/log/wechat-bot
    chown "$SERVICE_USER:$SERVICE_GROUP" /var/log/wechat-bot
    chmod 755 /var/log/wechat-bot
}

# 验证配置
verify_configuration() {
    log_info "验证配置..."
    
    if [ ! -f "$INSTALL_DIR/.env" ]; then
        log_warning "未找到.env配置文件"
        log_info "请创建 $INSTALL_DIR/.env 文件并配置必要的环境变量"
        log_info "可以参考项目中的.env.example文件"
        return 1
    fi
    
    # 测试Python环境
    if ! sudo -u "$SERVICE_USER" "$INSTALL_DIR/venv/bin/python" -c "import app" 2>/dev/null; then
        log_error "Python环境测试失败"
        return 1
    fi
    
    log_success "配置验证通过"
    return 0
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    systemctl start "$SERVICE_NAME"
    
    # 等待服务启动
    sleep 3
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务启动成功"
        systemctl status "$SERVICE_NAME" --no-pager
    else
        log_error "服务启动失败"
        log_info "查看日志: journalctl -u $SERVICE_NAME -f"
        return 1
    fi
}

# 显示使用说明
show_usage_info() {
    echo
    log_success "部署完成！"
    echo
    echo "服务管理命令:"
    echo "  启动服务: sudo systemctl start $SERVICE_NAME"
    echo "  停止服务: sudo systemctl stop $SERVICE_NAME"
    echo "  重启服务: sudo systemctl restart $SERVICE_NAME"
    echo "  查看状态: sudo systemctl status $SERVICE_NAME"
    echo "  查看日志: sudo journalctl -u $SERVICE_NAME -f"
    echo
    echo "配置文件位置: $INSTALL_DIR/.env"
    echo "项目目录: $INSTALL_DIR"
    echo
    if [ ! -f "$INSTALL_DIR/.env" ]; then
        echo -e "${YELLOW}重要提醒:${NC} 请创建并配置 $INSTALL_DIR/.env 文件后重启服务"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "YBA微信机器人服务部署脚本"
    echo "========================================"
    echo
    
    check_root
    check_requirements
    install_dependencies
    create_service_user
    create_install_directory
    copy_project_files
    # create_virtual_environment
    create_log_directory
    install_systemd_service
    
    if verify_configuration; then
        start_service
    else
        log_warning "配置验证失败，服务未启动"
        log_info "请完成配置后手动启动服务: sudo systemctl start $SERVICE_NAME"
    fi
    
    show_usage_info
}

# 运行主函数
main "$@"
