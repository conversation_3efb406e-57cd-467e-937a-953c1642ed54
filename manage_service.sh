#!/bin/bash

# YBA微信机器人服务管理脚本
# 提供便捷的服务管理命令

SERVICE_NAME="wechat-bot"
INSTALL_DIR="/opt/wechat-bot"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0 $1"
        exit 1
    fi
}

# 显示服务状态
show_status() {
    echo "========================================"
    echo "YBA微信机器人服务状态"
    echo "========================================"
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务正在运行"
    else
        log_warning "服务未运行"
    fi
    
    echo
    systemctl status "$SERVICE_NAME" --no-pager
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_warning "服务已在运行"
        return 0
    fi
    
    systemctl start "$SERVICE_NAME"
    sleep 2
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        echo "查看错误日志: sudo journalctl -u $SERVICE_NAME --no-pager -n 20"
        return 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止服务..."
    
    if ! systemctl is-active --quiet "$SERVICE_NAME"; then
        log_warning "服务未运行"
        return 0
    fi
    
    systemctl stop "$SERVICE_NAME"
    sleep 2
    
    if ! systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务已停止"
    else
        log_error "服务停止失败"
        return 1
    fi
}

# 重启服务
restart_service() {
    log_info "重启服务..."
    systemctl restart "$SERVICE_NAME"
    sleep 3
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务重启成功"
    else
        log_error "服务重启失败"
        return 1
    fi
}

# 查看日志
show_logs() {
    local lines=${2:-50}
    
    echo "========================================"
    echo "YBA微信机器人服务日志 (最近 $lines 行)"
    echo "========================================"
    
    journalctl -u "$SERVICE_NAME" --no-pager -n "$lines"
}

# 实时查看日志
follow_logs() {
    echo "========================================"
    echo "YBA微信机器人服务实时日志"
    echo "========================================"
    echo "按 Ctrl+C 退出"
    echo
    
    journalctl -u "$SERVICE_NAME" -f
}

# 更新代码
update_code() {
    log_info "更新代码..."
    
    # 停止服务
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "停止服务以更新代码..."
        systemctl stop "$SERVICE_NAME"
        local was_running=true
    else
        local was_running=false
    fi
    
    # 备份当前版本
    local backup_dir="/opt/wechat-bot-backup-$(date +%Y%m%d_%H%M%S)"
    log_info "备份当前版本到: $backup_dir"
    cp -r "$INSTALL_DIR" "$backup_dir"
    
    # 这里应该是从git拉取或复制新代码的逻辑
    log_warning "请手动更新 $INSTALL_DIR 中的代码文件"
    log_info "更新完成后，运行: $0 restart"
    
    # 如果之前在运行，询问是否重启
    if [ "$was_running" = true ]; then
        read -p "是否现在重启服务? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            restart_service
        fi
    fi
}

# 检查配置
check_config() {
    echo "========================================"
    echo "配置检查"
    echo "========================================"
    
    # 检查配置文件
    if [ -f "$INSTALL_DIR/.env" ]; then
        log_success ".env配置文件存在"
    else
        log_error ".env配置文件不存在"
        log_info "请创建 $INSTALL_DIR/.env 文件"
    fi
    
    # 检查Python环境
    if command -v python3 &> /dev/null; then
        log_success "Python3环境可用"

        # 测试导入
        cd "$INSTALL_DIR"
        if sudo -u www-data python3 -c "import app" 2>/dev/null; then
            log_success "Python模块导入测试通过"
        else
            log_error "Python模块导入测试失败"
        fi
    else
        log_error "Python3环境不可用"
    fi
    
    # 检查权限
    local owner=$(stat -c '%U:%G' "$INSTALL_DIR")
    if [ "$owner" = "www-data:www-data" ]; then
        log_success "文件权限正确"
    else
        log_warning "文件权限可能不正确: $owner"
    fi
}

# 显示帮助
show_help() {
    echo "YBA微信机器人服务管理脚本"
    echo
    echo "用法: $0 <命令> [参数]"
    echo
    echo "命令:"
    echo "  status          显示服务状态"
    echo "  start           启动服务"
    echo "  stop            停止服务"
    echo "  restart         重启服务"
    echo "  logs [行数]     查看日志 (默认50行)"
    echo "  follow          实时查看日志"
    echo "  update          更新代码"
    echo "  check           检查配置"
    echo "  help            显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 status       # 查看服务状态"
    echo "  $0 logs 100     # 查看最近100行日志"
    echo "  $0 restart      # 重启服务"
}

# 主函数
main() {
    case "${1:-help}" in
        "status")
            show_status
            ;;
        "start")
            check_permissions "$1"
            start_service
            ;;
        "stop")
            check_permissions "$1"
            stop_service
            ;;
        "restart")
            check_permissions "$1"
            restart_service
            ;;
        "logs")
            show_logs "$@"
            ;;
        "follow")
            follow_logs
            ;;
        "update")
            check_permissions "$1"
            update_code
            ;;
        "check")
            check_config
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
