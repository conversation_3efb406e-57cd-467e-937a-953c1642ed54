# 微信Bot Webhook调试程序

这是一个简单的调试程序，用于接收和查看微信Bot的webhook数据。

## 功能特性

- 接收webhook数据：`POST http://0.0.0.0:8080/recv/{wxid}`
- 支持指定wxid过滤消息
- 实时显示接收到的消息内容
- 记录日志到文件
- 提供状态查看接口

## 安装依赖

```bash
pip install flask
```

## 使用方法

### 1. 基本使用

```bash
# 监听所有wxid的消息
python debug_webhook.py

# 监听指定wxid的消息
python debug_webhook.py --wxid your_wxid

# 自定义端口
python debug_webhook.py --port 9000

# 启用调试模式
python debug_webhook.py --debug
```

### 2. 使用启动脚本

**Linux/Mac:**
```bash
chmod +x start_debug.sh
./start_debug.sh
```

**Windows:**
```cmd
start_debug.bat
```

### 3. 命令行参数

- `--wxid`: 指定要监听的wxid，不指定则监听所有
- `--host`: 监听地址 (默认: 0.0.0.0)
- `--port`: 监听端口 (默认: 8080)
- `--debug`: 启用Flask调试模式

## API接口

### 接收Webhook数据
- **URL**: `POST /recv/{wxid}`
- **说明**: 接收指定wxid的webhook数据
- **响应**: JSON格式的状态信息

### 状态查看
- **URL**: `GET /status`
- **说明**: 查看程序运行状态
- **响应**: 包含运行状态、目标wxid和当前时间

### 首页
- **URL**: `GET /`
- **说明**: 显示程序信息和接口说明

## 输出格式

程序会在控制台实时显示接收到的消息，格式如下：

```
============================================================
收到Webhook消息 - WXID: test_wxid
============================================================
时间: 2024-01-01 12:00:00
WXID: test_wxid
消息类型: text
发送者: user123
接收者: bot456
群组ID: group789
内容: 你好，这是一条测试消息
------------------------------------------------------------
原始数据:
{
  "wxid": "test_wxid",
  "type": "text",
  "content": "你好，这是一条测试消息",
  "from_user": "user123",
  "to_user": "bot456",
  "room_id": "group789"
}
============================================================
```

## 日志文件

程序会将接收到的消息记录到 `webhook_debug.log` 文件中，方便后续查看和分析。

## 测试webhook

可以使用curl命令测试webhook接口：

```bash
# 发送测试数据
curl -X POST http://localhost:8080/recv/test_wxid \
  -H "Content-Type: application/json" \
  -d '{
    "wxid": "test_wxid",
    "type": "text",
    "content": "测试消息",
    "from_user": "user123",
    "to_user": "bot456"
  }'
```

## 注意事项

1. 程序默认监听所有网络接口 (0.0.0.0)，请注意安全性
2. 如果指定了wxid参数，只会处理匹配的wxid消息
3. 程序会记录详细的日志信息，便于调试
4. 按 Ctrl+C 可以停止程序
