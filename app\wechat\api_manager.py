"""
统一微信API管理器
支持第一套API（token-based）、第二套API（wxid-based）和第三套API（V3 gewechat）的统一管理
"""
import logging
from typing import Dict, Any, Optional, Union
from .utils import WeChatClient  # 第一套API客户端
from .client_v2 import WeChatClientV2  # 第二套API客户端
from .client_v3 import WeChatClientV3  # 第三套API客户端

logger = logging.getLogger(__name__)


class APIManager:
    """统一微信API管理器"""
    
    def __init__(self, agent_config: Dict[str, Any], api_version: str = 'v1'):
        """
        初始化API管理器

        Args:
            agent_config: 代理商配置字典，包含所有必要的配置信息
            api_version: API版本 ('v1', 'v2' 或 'v3')
        """
        self.agent_config = agent_config
        self.agent_id = agent_config.get('id')
        self.agent_name = agent_config.get('name', 'Unknown')
        self.api_version = api_version
        
        # 初始化对应的API客户端
        self.client = self._create_client()
        
        logger.info(f"初始化API管理器 - 代理商: {self.agent_name}, API版本: {self.api_version}")
    
    def _create_client(self) -> Union[WeChatClient, WeChatClientV2, WeChatClientV3]:
        """根据API版本创建对应的客户端"""

        if self.api_version == 'v3':
            # V3 API客户端
            base_url = self.agent_config.get('wechat_base_url')
            token = self.agent_config.get('token')
            app_id = self.agent_config.get('app_id')

            if not base_url:
                raise ValueError(f"代理商 {self.agent_name} 缺少wechat_base_url配置")

            client = WeChatClientV3(base_url=base_url, token=token, app_id=app_id)
            logger.info(f"创建V3 API客户端: {base_url}")
            return client

        elif self.api_version == 'v2':
            # V2 API客户端
            base_url = self.agent_config.get('wechat_base_url')  # 复用现有字段
            wxid = self.agent_config.get('wxid')

            if not base_url:
                raise ValueError(f"代理商 {self.agent_name} 缺少wechat_base_url配置")

            client = WeChatClientV2(base_url=base_url, wxid=wxid)
            logger.info(f"创建V2 API客户端: {base_url}")
            return client

        else:
            # 第一套API客户端（默认）
            base_url = self.agent_config.get('wechat_base_url')
            token = self.agent_config.get('token')

            if not base_url or not token:
                raise ValueError(f"代理商 {self.agent_name} 缺少wechat_base_url或token配置")

            client = WeChatClient(base_url=base_url, api_key=token)
            logger.info(f"创建第一套API客户端: {base_url}")
            return client
    
    # ==================== 统一接口方法 ====================
    
    def get_login_status(self) -> Dict[str, Any]:
        """获取登录状态（统一接口）"""
        try:
            result = self.client.get_login_status()
            
            # 标准化响应格式
            return self._standardize_response(result, 'login_status')
            
        except Exception as e:
            logger.error(f"获取登录状态失败 - {self.agent_name}: {str(e)}")
            return {
                'Code': 500,
                'Text': f'获取登录状态异常: {str(e)}',
                'Data': {
                    'loginState': 0,
                    'status': 'error',
                    'message': str(e)
                }
            }
    
    def get_user_info(self) -> Dict[str, Any]:
        """获取用户信息（统一接口）"""
        try:
            if self.api_version == 'v3':
                result = self.client.get_user_info()
            elif self.api_version == 'v2':
                result = self.client.get_user_profile()
            else:
                result = self.client.get_user_info()

            return self._standardize_response(result, 'user_info')

        except Exception as e:
            logger.error(f"获取用户信息失败 - {self.agent_name}: {str(e)}")
            return {
                'Code': 500,
                'Text': f'获取用户信息异常: {str(e)}',
                'Data': None
            }
    
    def logout(self) -> Dict[str, Any]:
        """退出登录（统一接口）"""
        try:
            result = self.client.logout()
            return self._standardize_response(result, 'logout')
            
        except Exception as e:
            logger.error(f"退出登录失败 - {self.agent_name}: {str(e)}")
            return {
                'Code': 500,
                'Text': f'退出登录异常: {str(e)}',
                'Data': None
            }
    
    def get_qr_code(self, **kwargs) -> Dict[str, Any]:
        """获取二维码（统一接口）"""
        try:
            if self.api_version == 'v3':
                # V3 API的二维码获取
                result = self.client.get_qr_code(**kwargs)
            elif self.api_version == 'v2':
                # V2 API的二维码获取
                device_id = kwargs.get('device_id', self.agent_config.get('device_id'))
                # device_name = kwargs.get('device_name', 'YBA_Device')
                # login_type = kwargs.get('login_type', 'iPad')
                proxy = kwargs.get('proxy')

                # 如果没有传入代理，尝试从agent配置中解析
                if not proxy and self.agent_config.get('proxy'):
                    from app.models import Agent
                    # 创建临时Agent对象来解析代理
                    temp_agent = Agent(
                        id=self.agent_config.get('id'),
                        name=self.agent_config.get('name'),
                        proxy=self.agent_config.get('proxy')
                    )
                    proxy = temp_agent.get_proxy_for_v2_api()
                    logger.debug(f"从配置解析代理信息: {proxy}")

                result = self.client.get_qr_code(
                    device_id=device_id,
                    # device_name=device_name,
                    # login_type=login_type,
                    proxy=proxy
                )
            else:
                # 第一套API的二维码获取
                proxy = kwargs.get('proxy')
                result = self.client.get_qr_code(proxy=proxy)

            return self._standardize_response(result, 'qr_code')

        except Exception as e:
            logger.error(f"获取二维码失败 - {self.agent_name}: {str(e)}")
            return {
                'Code': 500,
                'Text': f'获取二维码异常: {str(e)}',
                'Data': None
            }
    
    def check_qr_status(self, uuid: str, captcha: str = "") -> Dict[str, Any]:
        """检查二维码状态（统一接口）"""
        try:
            if self.api_version == 'v3':
                result = self.client.check_qr_status(uuid, captcha)
            elif self.api_version == 'v2':
                result = self.client.check_qr_status(uuid)
            else:
                result = self.client.check_qrcode_status()

            return self._standardize_response(result, 'qr_status')

        except Exception as e:
            logger.error(f"检查二维码状态失败 - {self.agent_name}: {str(e)}")
            return {
                'Code': 500,
                'Text': f'检查二维码状态异常: {str(e)}',
                'Data': None
            }
    
    # ==================== 第二套API特有方法 ====================
    
    def a16_login(self, username: str, password: str, a16_data: str, **kwargs) -> Dict[str, Any]:
        """A16登录（仅第二套API）"""
        if self.api_version != 'v2':
            return {
                'Code': -1,
                'Text': 'A16登录仅支持V2 API',
                'Data': None
            }

        try:
            device_name = kwargs.get('device_name', 'YBA_Device')
            proxy = kwargs.get('proxy')
            
            result = self.client.a16_login(
                username=username,
                password=password,
                a16_data=a16_data,
                device_name=device_name,
                proxy=proxy
            )
            
            return self._standardize_response(result, 'a16_login')
            
        except Exception as e:
            logger.error(f"A16登录失败 - {self.agent_name}: {str(e)}")
            return {
                'Code': 500,
                'Text': f'A16登录异常: {str(e)}',
                'Data': None
            }
    
    def data62_login(self, username: str, password: str, data62: str, **kwargs) -> Dict[str, Any]:
        """62数据登录（仅第二套API）"""
        if self.api_version != 'v2':
            return {
                'Code': -1,
                'Text': '62数据登录仅支持V2 API',
                'Data': None
            }

        try:
            device_name = kwargs.get('device_name', 'YBA_Device')
            proxy = kwargs.get('proxy')
            
            result = self.client.data62_login(
                username=username,
                password=password,
                data62=data62,
                device_name=device_name,
                proxy=proxy
            )
            
            return self._standardize_response(result, 'data62_login')
            
        except Exception as e:
            logger.error(f"62数据登录失败 - {self.agent_name}: {str(e)}")
            return {
                'Code': 500,
                'Text': f'62数据登录异常: {str(e)}',
                'Data': None
            }
    
    def set_wxid(self, wxid: str):
        """设置微信ID（仅V2 API）"""
        if self.api_version == 'v2' and hasattr(self.client, 'set_wxid'):
            self.client.set_wxid(wxid)
            self.agent_config['wxid'] = wxid
            logger.info(f"设置wxid - {self.agent_name}: {wxid}")

    def set_app_id(self, app_id: str):
        """设置应用ID（仅V3 API）"""
        if self.api_version == 'v3' and hasattr(self.client, 'set_app_id'):
            self.client.set_app_id(app_id)
            self.agent_config['app_id'] = app_id
            logger.info(f"设置app_id - {self.agent_name}: {app_id}")

    def get_token(self) -> Dict[str, Any]:
        """获取token（仅V3 API）"""
        if self.api_version != 'v3':
            return {
                'ret': -1,
                'msg': 'get_token仅支持V3 API',
                'data': None
            }

        try:
            result = self.client.get_token()
            return self._standardize_v3_response(result, 'get_token')

        except Exception as e:
            logger.error(f"获取token失败 - {self.agent_name}: {str(e)}")
            return {
                'ret': 500,
                'msg': f'获取token异常: {str(e)}',
                'data': None
            }

    def set_callback(self, callback_url: str) -> Dict[str, Any]:
        """设置回调地址（仅V3 API）"""
        if self.api_version != 'v3':
            return {
                'ret': -1,
                'msg': 'set_callback仅支持V3 API',
                'data': None
            }

        try:
            result = self.client.set_callback(callback_url)
            return self._standardize_v3_response(result, 'set_callback')

        except Exception as e:
            logger.error(f"设置回调地址失败 - {self.agent_name}: {str(e)}")
            return {
                'ret': 500,
                'msg': f'设置回调地址异常: {str(e)}',
                'data': None
            }
    
    # ==================== 工具方法 ====================
    
    def _standardize_response(self, response: Dict[str, Any], operation: str) -> Dict[str, Any]:
        """标准化API响应格式"""
        if not isinstance(response, dict):
            return {
                'Code': 500,
                'Text': f'无效的API响应格式',
                'Data': None
            }
        
        # 确保响应包含必要的字段
        standardized = {
            'Code': response.get('Code', 500),
            'Text': response.get('Text', 'Unknown'),
            'Data': response.get('Data'),
            'api_version': self.api_version,
            'agent_id': self.agent_id,
            'agent_name': self.agent_name,
            'operation': operation
        }
        
        return standardized

    def _standardize_v3_response(self, response: Dict[str, Any], operation: str) -> Dict[str, Any]:
        """标准化V3 API响应格式"""
        if not isinstance(response, dict):
            return {
                'ret': 500,
                'msg': f'无效的API响应格式',
                'data': None
            }

        # V3 API使用ret字段而不是Code字段
        standardized = {
            'ret': response.get('ret', 500),
            'msg': response.get('msg', 'Unknown'),
            'data': response.get('data'),
            'api_version': self.api_version,
            'agent_id': self.agent_id,
            'agent_name': self.agent_name,
            'operation': operation
        }

        return standardized

    def get_api_info(self) -> Dict[str, Any]:
        """获取API信息"""
        return {
            'agent_id': self.agent_id,
            'agent_name': self.agent_name,
            'api_version': self.api_version,
            'base_url': self.agent_config.get('wechat_base_url'),
            'is_active': self.agent_config.get('wechat_is_active', False),
            'auth_method': 'wxid' if self.api_version == 'v2' else ('app_id' if self.api_version == 'v3' else 'token')
        }
    
    def is_available(self) -> bool:
        """检查API是否可用"""
        try:
            status = self.get_login_status()
            return status.get('Code') != -1  # -1表示连接失败
        except Exception:
            return False
